provider "aws" {
    region = var.aws_settings.region

    default_tags {
        tags = {
            Squad      = var.hf_squad
            Tribe      = var.hf_tribe
            Repository = var.hf_repo
        }
    }
}

provider "vault" {
    namespace = "services/scm-procurement-data-integration"
}

data "vault_generic_secret" "vault_secrets" {
    path = format("prod/key-value/secrets")
}

data "aws_s3_object" "vault_token" {
    bucket = "hf-terraform-${var.env_suffix}"
    key    = "vault/token"
}

data "aws_s3_object" "vault_server" {
    bucket = "hf-terraform-${var.env_suffix}"
    key    = "vault/server"
}

data "aws_iam_role" "rds-monitoring" {
    name = "rds-monitoring-role"
}

data "aws_vpc" "default" {
    default = true
}

data "aws_security_group" "default" {
    id     = "sg-3a27b45f"
    vpc_id = data.aws_vpc.default.id
}

data "aws_security_group" "Databases" {
    id     = "sg-b1c050d4"
    vpc_id = data.aws_vpc.default.id
}

resource "aws_db_instance" "service-db" {
    identifier                   = "scm-procurement-data-integration-${var.env_suffix}"
    engine                       = "postgres"
    engine_version               = var.db_settings.engine_version
    auto_minor_version_upgrade   = true
    allow_major_version_upgrade  = true
    instance_class               = var.db_settings.instance_class
    allocated_storage            = "50"
    max_allocated_storage        = "100"
    storage_encrypted            = false
    db_name                      = "scm_procurement_data_integration"
    username                     = data.vault_generic_secret.vault_secrets.data.DB_USERNAME
    password                     = data.vault_generic_secret.vault_secrets.data.DB_PASSWORD
    port                         = "5432"
    vpc_security_group_ids = [data.aws_security_group.default.id, data.aws_security_group.Databases.id]
    maintenance_window           = "wed:10:00-wed:10:30"
    backup_window                = "01:23-01:53"
    backup_retention_period      = var.db_settings.backup_retention_period
    option_group_name            = var.db_settings.option_group_name
    parameter_group_name         = var.db_settings.parameter_group_name
    copy_tags_to_snapshot        = false
    db_subnet_group_name         = "default"
    storage_type                 = "gp2"
    skip_final_snapshot          = true
    apply_immediately            = var.db_settings.apply_immediately
    monitoring_interval          = "30"
    monitoring_role_arn          = data.aws_iam_role.rds-monitoring.arn
    performance_insights_enabled = true
    ca_cert_identifier           = "rds-ca-rsa2048-g1"

    tags = {
        Environment = title(var.env_suffix)
        Group     = "scm-procurement-data-integration-db"
        CostGroup = "Global Core"
        Backup    = "Offsite"
    }
}

output "this_db_instance_address" {
    description = "The address of the RDS instance"
    value       = aws_db_instance.service-db.address
}

data "aws_route53_zone" "hf-io" {
    name = "hellofresh.io."
}

resource "aws_route53_record" "service-db-r53record" {
    zone_id = data.aws_route53_zone.hf-io.zone_id
    name    = "scm-procurement-data-integration-db.${var.env_suffix}.hellofresh.io"
    type    = "CNAME"
    ttl     = "60"
    records = [aws_db_instance.service-db.address]
}
