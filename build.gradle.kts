plugins {
    alias(libs.plugins.jacocolog)
    alias(libs.plugins.sonarqube)
}

sonarqube {
    properties {
        property(
            "sonar.exclusions",
            "**/com/hellofresh/scm/**/model*/*.kt," +
                    "**/com/hellofresh/scm/*/*Configuration.kt," +
                    "**/com/hellofresh/scm/*/*Application.kt,",
        )
        property("sonar.host.url", "https://sonarqube.tools-k8s.hellofresh.io")
        property("sonar.links.homepage", "https://github.com/hellofresh/scm-procurement-data-integration")
        property("sonar.links.issue", "https://hellofresh.atlassian.net/jira/software/c/projects/GPPT/boards/5991")
        property("sonar.links.ci", "https://github.com/hellofresh/scm-procurement-data-integration/actions/workflows/pr-build-test-analyze.yml")
        property("sonar.links.scm", "https://github.com/hellofresh/scm-procurement-data-integration")
        property("sonar.links.scm_dev", "**************:hellofresh/scm-procurement-data-integration.git")
        property(
            "sonar.coverage.jacoco.xmlReportPaths",
            "${project.layout.buildDirectory.get().asFile}/reports/jacoco/jacocoAggregatedReport/jacocoAggregatedReport.xml",
        )
        property("sonar.gradle.skipCompile", "true")
    }
}
