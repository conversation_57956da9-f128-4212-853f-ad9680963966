---
apiVersion: core.oam.dev/v1beta1
kind: Application
metadata:
  labels:
    partition: food-systems
    squad: supply-automation
    tribe: planning-and-purchasing
    oam.hellofresh.io/version: v2
  name: gsheet-integration-app
spec:
  components:
    - name: app
      type: hf_webservice
      properties:
        image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/scm-procurement-data-integration:gsheet-integration-app
        memory: 1Gi
        memoryLimit: 2Gi
        cpu: 1
        env:
          SPRING_PROFILES_ACTIVE: staging
          STATSIG_ENVIRONMENT: staging
        probes:
          liveness:
            path: /actuator/health/liveness
            port: 8081
            initialDelaySeconds: 30
            timeoutSeconds: 1
          readiness:
            path: /actuator/health/readiness
            port: 8081
            timeoutSeconds: 30
            initialDelaySeconds: 30
            periodSeconds: 30
            failureThreshold: 3
        ports:
          - containerPort: 8080
            port: 8080
            protocol: http
          - containerPort: 8081
            port: 8081
            protocol: http
      traits:
        - type: scaler
          properties:
            minReplicas: 2
            maxReplicas: 3
        - type: intranet_ingress
        - type: secrets
          properties:
            secrets:
              - name: secrets
            namespace: services/scm-procurement-data-integration
        - type: dependencies
          properties:
            endpoints:
              - uri: tcp://scm-procurement-data-integration-db.staging.hellofresh.io:5432
        - type: service_monitor
          properties:
            port: 8081
            path: /actuator/prometheus
