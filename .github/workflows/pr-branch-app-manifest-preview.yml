---
name: "PR: Application Manifest Preview"

concurrency:
  group: pr-app-manifest-preview-${{ github.head_ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - master

jobs:
  app-manifest-preview:
    name: App Manifest Preview
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: write
      id-token: write
      pull-requests: write
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Build application manifests
        uses: hellofresh/jetstream-ci-scripts/actions/build-application-manifests@master
        with:
          version: PR-${{ github.event.pull_request.number }}

      - name: Determine environments
        id: environments
        run: |
          HAS_PROD_ENV="$(git ls-remote --heads origin ftcp/deploy/prod | wc -l)"
          HAS_STAGE_ENV="$(git ls-remote --heads origin ftcp/deploy/stage | wc -l)"
          HAS_SHARED_ENV="$(git ls-remote --heads origin ftcp/deploy/shared | wc -l)"
          if [[ "$HAS_PROD_ENV" != "0" ]]; then
            echo "prod=true" >> "$GITHUB_OUTPUT"
          fi
          if [[ "$HAS_SHARED_ENV" != "0" ]]; then
            echo "shared=true" >> "$GITHUB_OUTPUT"
          fi
          if [[ "$HAS_STAGE_ENV" != "0" ]]; then
            echo "stage=true" >> "$GITHUB_OUTPUT"
          fi
      # Production Preview
      - if: steps.environments.outputs.prod == 'true'
        name: "Prod: Push to detached branch"
        id: push-to-branch-prod
        uses: hellofresh/jetstream-ci-scripts/actions/push-to-branch@master
        with:
          path: app-manifests/prod/
          branch: ftcp/deploy/prod
          detached-branch: true

      - if: steps.environments.outputs.prod == 'true'
        name: "Prod: Diff Report"
        uses: hellofresh/jetstream-ci-scripts/actions/diff-branch@master
        with:
          branch-a: ftcp/deploy/prod
          branch-b: ${{ steps.push-to-branch-prod.outputs.commit-hash }}
          branch-a-name: prod
          branch-b-name: PR-${{ github.event.pull_request.number }}
          report-format: app-manifest
          output-file: diff-report-prod.txt

      # Stage Preview
      - if: steps.environments.outputs.stage == 'true'
        name: "Stage: Push to detached branch"
        id: push-to-branch-stage
        uses: hellofresh/jetstream-ci-scripts/actions/push-to-branch@master
        with:
          path: app-manifests/stage/
          branch: ftcp/deploy/stage
          detached-branch: true

      - if: steps.environments.outputs.stage == 'true'
        name: "Stage: Diff Report"
        uses: hellofresh/jetstream-ci-scripts/actions/diff-branch@master
        with:
          branch-a: ftcp/deploy/stage
          branch-b: ${{ steps.push-to-branch-stage.outputs.commit-hash }}
          branch-a-name: stage
          branch-b-name: PR-${{ github.event.pull_request.number }}
          report-format: app-manifest
          output-file: diff-report-stage.txt

      # Shared Preview
      - if: steps.environments.outputs.shared == 'true'
        name: "Shared: Push to detached branch"
        id: push-to-branch-shared
        uses: hellofresh/jetstream-ci-scripts/actions/push-to-branch@master
        with:
          path: app-manifests/shared/
          branch: ftcp/deploy/shared
          detached-branch: true

      - if: steps.environments.outputs.shared == 'true'
        name: "Shared: Diff Report"
        uses: hellofresh/jetstream-ci-scripts/actions/diff-branch@master
        with:
          branch-a: ftcp/deploy/shared
          branch-b: ${{ steps.push-to-branch-shared.outputs.commit-hash }}
          branch-a-name: shared
          branch-b-name: PR-${{ github.event.pull_request.number }}
          report-format: app-manifest
          output-file: diff-report-shared.txt

      # Consolidate and post
      - name: Compile results
        run: |
          if [[ -f "diff-report-prod.txt" ]]; then
            echo "### Prod" >> diff-report.txt
            tail -n +2 < diff-report-prod.txt >> diff-report.txt
          fi
          if [[ -f "diff-report-stage.txt" ]]; then
            echo "### Stage" >> diff-report.txt
            tail -n +2 < diff-report-stage.txt >> diff-report.txt
          fi
          if [[ -f "diff-report-shared.txt" ]]; then
            echo "### Shared" >> diff-report.txt
            tail -n +2 < diff-report-shared.txt >> diff-report.txt
          fi
          if [[ -f "diff-report.txt" ]]; then
            HEADER_TEXT="## Application Manifest Preview"
            sed -i "1i $HEADER_TEXT" diff-report.txt
          fi
      - name: Post comment
        if: |
          steps.environments.outputs.prod == 'true' ||
          steps.environments.outputs.stage == 'true' ||
          steps.environments.outputs.shared == 'true'
        uses: thollander/actions-comment-pull-request@v3
        with:
          file-path: diff-report.txt
          comment-tag: app-manifest-preview
          mode: recreate
