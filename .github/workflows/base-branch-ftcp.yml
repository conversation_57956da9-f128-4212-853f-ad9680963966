---
name: Deploy FTCP

on:
  push:
    branches:
      - master

permissions:
  id-token: write
  contents: read

jobs:

  prepare_modified_files:
    name: 🗃 Modules modified
    runs-on: [ self-hosted, default ]
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 📖 Read config file
        id: config
        run: echo "config=$(jq -c . ./.github/config/modules.json)" >> $GITHUB_OUTPUT

      - name: 👀 Get modified modules
        id: check_modified
        uses: ./.github/actions/check_modified
        with:
          config: ${{ steps.config.outputs.config }}
          terraform_path: 'terraform/**'
          common_paths: ".github/**, model/**, .java-version,gradle/**, build.gradle.kts, gradle.properties, project.properties, settings.gradle.kts, deployment/**, gsheet-integration-app/**"

    outputs:
      modules: ${{ steps.check_modified.outputs.modified_modules }}
      is_terraform_changed: ${{ steps.check_modified.outputs.terraform == 'true' }}

  generate_version:
    name: 🪪 Generate Version
    runs-on: [ self-hosted, default ]
    steps:
      - name: 🪪 Generate Version
        id: version
        uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master
    outputs:
      version: ${{ steps.version.outputs.new_version }}
  
  release_module:
    name: 📦 Create release ${{ matrix.modules.name }}
    if: ${{ needs.prepare_modified_files.outputs.modules != '[]' && needs.prepare_modified_files.outputs.modules != '' }}
    runs-on: [ self-hosted, default ]
    needs:
      - prepare_modified_files
      - generate_version
    strategy:
      matrix:
        modules: ${{ fromJSON(needs.prepare_modified_files.outputs.modules) }}
    steps:
      - name: 🚪 Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 👷‍♂️ Create release
        id: release
        uses: './.github/actions/release_module_ftcp'
        with:
          module: ${{ matrix.modules.module }}
          version: ${{ needs.generate_version.outputs.version }}
  
  release-manifest:
    if: ${{ always() && !failure() && !cancelled() && needs.prepare_modified_files.outputs.modules != '[]' && needs.prepare_modified_files.outputs.modules != ''}}
    name: Build application manifests
    needs:
      - prepare_modified_files
      - release_module
      - generate_version
    runs-on: [self-hosted, default]
    timeout-minutes: 15
    permissions:
      contents: write
      id-token: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Build Manifest
        uses: hellofresh/jetstream-ci-scripts/actions/build-application-manifests@master
        with:
          path: deployment
          version: ${{ needs.generate_version.outputs.version}}

      - name: 📥 Upload manifest artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ needs.generate_version.outputs.version }}-manifest
          path: ./app-manifests.tar.gz
  
  deploy-stage:
    name: Deploy Stage
    if: ${{ always() && !failure() && !cancelled() && needs.prepare_modified_files.outputs.modules != '[]' && needs.prepare_modified_files.outputs.modules != ''}}
    needs:
      - prepare_modified_files
      - release_module
      - release-manifest
      - generate_version
    runs-on: [self-hosted, default]
    timeout-minutes: 15
    permissions:
      contents: write
      id-token: write
      deployments: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;
            
      - name: Checkout source code
        uses: actions/checkout@v4
        
      - name: Download Manifest Artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ needs.generate_version.outputs.version }}-manifest

      - name: Extract manifests
        run: |
          tar -xzf "app-manifests.tar.gz"
          
      - name: Push to branch
        uses: hellofresh/jetstream-ci-scripts/actions/push-to-branch@master
        with:
          path: app-manifests/stage
          branch: ftcp/deploy/stage
          message: ${{ needs.generate_version.outputs.version }} ${{ github.sha }}

      - name: Setup k8s
        uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
        with:
          environment: 'tools'

      - name: Deploy ArgoCD Applications to the Cluster
        run: |
          GLOB_CUEP_PROJECTS="app-manifests/stage/argocd/appproject-argoproj*.yaml"
          # shellcheck disable=SC2206
          kubectl apply -f "$GLOB_CUEP_PROJECTS" -n argo-cd

          GLOB="app-manifests/stage/argocd/applicationset-argo*.yaml"
          # shellcheck disable=SC2086
          kubectl apply -f "$GLOB" -n argo-cd

  # Pause and require Manual Approval to proceed with Production Deployment
  approve-live:
    name: Approve Live Deployment
    runs-on: [ self-hosted, default ]
    environment: prod-approval
    needs:
      - deploy-stage
    steps:
      - run: echo "Deployment approved, deploying to live"

  deploy-live:
    name: Deploy Live
    if: ${{ always() && !failure() && !cancelled() && needs.prepare_modified_files.outputs.modules != '[]' && needs.prepare_modified_files.outputs.modules != ''}}
    needs:
      - prepare_modified_files
      - generate_version
      - approve-live
    runs-on: [self-hosted, default]
    timeout-minutes: 15
    permissions:
      contents: write
      id-token: write
      deployments: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Download Manifest Artifact
        uses: actions/download-artifact@v4
        with:
          name: ${{ needs.generate_version.outputs.version }}-manifest

      - name: Extract manifests
        run: |
          tar -xzf "app-manifests.tar.gz"

      - name: Push to branch
        uses: hellofresh/jetstream-ci-scripts/actions/push-to-branch@master
        with:
          path: app-manifests/prod
          branch: ftcp/deploy/prod
          message: ${{ needs.generate_version.outputs.version }} ${{ github.sha }}

      - name: Setup k8s
        uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
        with:
          environment: 'tools'

      - name: Deploy ArgoCD Applications to the Cluster
        run: |
          GLOB_CUEP_PROJECTS="app-manifests/prod/argocd/appproject-argoproj*.yaml"
          # shellcheck disable=SC2206
          kubectl apply -f "$GLOB_CUEP_PROJECTS" -n argo-cd

          GLOB="app-manifests/prod/argocd/applicationset-argo*.yaml"
          # shellcheck disable=SC2086
          kubectl apply -f "$GLOB" -n argo-cd
