name: Import Vault Secrets
description: Import vault secrets
inputs:
  export-token:
      description: 'Whether to export the vault token'
      required: false
      default: false
  secrets:
      description: '(path) (secret-to-import) | (optional-secret-name)'
      required: false
  shared-secrets:
      description: '(path) (secret-to-import) | (optional-secret-name)'
      required: false

runs:
  using: 'composite'
  steps:
    - name: 🤫 Import secrets
      uses: hellofresh/jetstream-ci-scripts/actions/vault@master
      with:
        namespace: services/scm-procurement-data-integration
        export-token: ${{ inputs.export-token }}
        secrets: ${{ inputs.secrets }}
        shared-secrets: ${{ inputs.shared-secrets }}
