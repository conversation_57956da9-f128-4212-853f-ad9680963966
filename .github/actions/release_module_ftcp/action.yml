name: "Release a module"
description: "Create a release for a module"
inputs:
  module:
    description: "Module to be released"
    required: true
  version:
    description: "Version to be released"
    required: true
outputs:
  version:
    description: "The version of the released module"
    value: ${{ steps.version.outputs.new_version }}

runs:
  using: "composite"
  steps:
    - name: 🚪 Checkout source code
      uses: actions/checkout@v4

    - name: 🤫 Import Secrets
      id: vault-secrets
      uses: ./.github/actions/vault
      with:
        secrets: |
          common/key-value/data/secrets SLACK_URL | SLACK_URL;
        shared-secrets: |
          common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN;
          common/data/defaults artifactory_username | ARTIFACTORY_USERNAME;
          common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD;

    - name: 🪪 Generate Version
      id: version
      uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master

    - name: 🏗 Build and push docker image to ECR
      shell: bash
      run: |
        gradle ${{ inputs.module }}:jib --image=489198589229.dkr.ecr.eu-west-1.amazonaws.com/scm-procurement-data-integration \
          -Djib.to.tags=${{ inputs.module }}-${{ inputs.version }},${{ inputs.module }}-latest

    - name: 🪪 Save Version
      shell: bash
      env:
        MODULE: ${{ inputs.module }}
      run: |
        version=${{ inputs.version }}
        echo "new_version=$version" >> $GITHUB_OUTPUT
        echo $version > ${MODULE}-version.txt

    - name: 📥 Upload version artifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{ inputs.module }}-version
        path: ./${{ inputs.module }}-version.txt
