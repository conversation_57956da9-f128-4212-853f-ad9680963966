import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.dsl.KotlinVersion
import org.jetbrains.kotlin.gradle.plugin.getKotlinPluginVersion
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("com.hellofresh.scm.java-conventions")
    // Apply the Kotlin JVM plugin to add support for Kotlin on the JVM.
    kotlin("jvm")
    id("com.hellofresh.scm.detekt-conventions")
}

val javaVersion = File("$rootDir/.java-version").readText().trim()
val kotlinVersion = File("$rootDir/.kotlin-version").readText().trim()

val embeddedMajorAndMinorKotlinVersion = project.getKotlinPluginVersion().substringBeforeLast(".")
if (kotlinVersion != embeddedMajorAndMinorKotlinVersion) {
    logger.warn(
        "Constant 'KOTLIN_VERSION' ($kotlinVersion) differs from embedded " +
            "Kotlin version in Gradle (${project.getKotlinPluginVersion()})!\n" +
            "Constant 'KOTLIN_VERSION' should be ($embeddedMajorAndMinorKotlinVersion)."
    )
}

kotlin {
    jvmToolchain {
        // Intentionally left blank to combat issues with lazy configuration,
        // see https://youtrack.jetbrains.com/issue/KT-43095 for details.
    }
}

tasks.withType<KotlinCompile> {
    logger.lifecycle("Configuring $name with version ${project.getKotlinPluginVersion()} in project ${project.name}")
    compilerOptions {
        jvmTarget.set(JvmTarget.fromTarget(javaVersion))
        freeCompilerArgs.set(
            listOf(
                "-Xjsr305=strict", "-Xno-call-assertions", "-Xno-param-assertions", "-Xno-receiver-assertions"
            )
        )
        allWarningsAsErrors.set(true)
        languageVersion.set(KotlinVersion.fromVersion(kotlinVersion))
        apiVersion.set(KotlinVersion.fromVersion(kotlinVersion))
    }
}

dependencies {
    constraints {
        implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    }
    // Align versions of all Kotlin components
    implementation(platform(kotlin("bom")))
    // Use Kotlin Test
    implementation("org.jetbrains.kotlin:kotlin-test")
    implementation("org.jetbrains.kotlin:kotlin-test-junit5")
    // Use the Kotlin JDK 8 standard library.
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
}
