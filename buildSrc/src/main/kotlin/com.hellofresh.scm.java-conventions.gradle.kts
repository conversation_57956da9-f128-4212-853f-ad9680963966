import java.io.File

val libs = extensions.getByType<VersionCatalogsExtension>().named("libs")

plugins {
    `java-library`
}

val javaVersion = File("$rootDir/.java-version").readText().trim()

java {
    // Auto JDK setup
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(javaVersion))
    }
    withSourcesJar()
    withJavadocJar()
}

tasks.compileJava {
    // See: https://docs.oracle.com/en/java/javase/12/tools/javac.html
    @Suppress("SpellCheckingInspection")
    options.compilerArgs.addAll(
        listOf(
            "-Xlint:all", // Enables all recommended warnings.
            "-Werror" // Terminates compilation when warnings occur.
        )
    )
    options.encoding = "UTF-8"
}

tasks.jar {
    manifest {
        attributes(
            mapOf(
                "Implementation-Title" to project.name,
                "Implementation-Version" to project.version
            )
        )
    }
}

dependencies {
    implementation(platform("org.apache.logging.log4j:log4j-bom:${libs.findVersion("log4j").get()}"))
    api("org.apache.logging.log4j:log4j-slf4j-impl")
}
