plugins {
    id("com.hellofresh.scm.common-conventions")
    id("com.hellofresh.scm.spring-conventions")
    id("com.google.cloud.tools.jib")
}

repositories {
    gradlePluginPortal()
}

tasks {
    test {
        useJUnitPlatform {
            excludeTags("integration")
        }
        systemProperty("spring.profiles.active", "test")
    }
}

tasks.register("integrationTest", Test::class.java).configure {
    description = "Runs integration tests."
    group = "verification"
    shouldRunAfter(tasks.test)
    useJUnitPlatform {
        includeTags("integration")
    }
    systemProperty("spring.profiles.active", "test")
}

jib {
    from {
        image = project.property("jib.from.image")!!.toString()
    }
    to {
        image = "${project.property("jib.to.repository")!!}:${System.getenv("RELEASE_TAG")}"
    }
    container {
        project.findProperty("jib.container.jvmFlags")?.toString()?.split(' ')?.let {
            jvmFlags = it
        }
    }
}
