import io.gitlab.arturbosch.detekt.Detekt

val libs = extensions.getByType<VersionCatalogsExtension>().named("libs")

plugins {
    // Required for lifecycle (check and clean) tasks.
    id("java-base")
    id("io.gitlab.arturbosch.detekt")
}

val javaVersion = File("$rootDir/.java-version").readText().trim()

tasks {
    withType<Detekt>().configureEach {
        config.from("$rootDir/.detekt.yaml")

        ignoreFailures = false
        jvmTarget = javaVersion
        buildUponDefaultConfig = true
        parallel = true

        setSource(files(projectDir))
        include("**/*.kt", "**/*.kts")
        exclude("**/resources/**", "**/build/**")

        val ci = System.getenv().containsKey("CI")
        reports {
            txt.required.set(false)
            html.required.set(!ci)
            xml.required.set(ci)
        }
    }

    register<Detekt>("detektFormat") {
        description = "Reformat all Kotlin files."
        autoCorrect = true
        ignoreFailures = true
    }

    check {
        dependsOn("detekt")
    }

    val cleanDetekt by registering(Delete::class) {
        setDelete(detekt.get().reportsDir.get())
    }

    clean {
        dependsOn(cleanDetekt)
    }
}

dependencies {
    detektPlugins("io.gitlab.arturbosch.detekt:detekt-formatting:${libs.findVersion("detekt").get()}")

    // TODO remove explicit dependencies once detekt fixed the issue with JVM
    //   target 17:
    //   - https://hellofresh.atlassian.net/browse/KT-113
    //   - https://github.com/detekt/detekt/isswues/4287#issuecomment-981657386
    detekt("io.gitlab.arturbosch.detekt:detekt-cli:${libs.findVersion("detekt").get()}")
    detekt("org.jetbrains.kotlin:kotlin-compiler-embeddable")
}
