import com.adarshr.gradle.testlogger.theme.ThemeType.STANDARD_PARALLEL
import org.gradle.api.tasks.testing.logging.TestExceptionFormat.FULL
import org.gradle.api.tasks.testing.logging.TestLogEvent.FAILED

val libs = extensions.getByType<VersionCatalogsExtension>().named("libs")

plugins {
    id("com.hellofresh.scm.kotlin-conventions")
    id("com.adarshr.test-logger")
}

repositories {
    maven("https://artifactory.tools-k8s.hellofresh.io/artifactory/maven-local/")
    mavenCentral()
    maven("https://repo.spring.io/milestone/")
    maven("https://packages.confluent.io/maven/")
}

dependencies {
    testImplementation(platform("org.junit:junit-bom:${libs.findVersion("junit").get()}"))
    testImplementation("org.junit.jupiter:junit-jupiter")
}

tasks.withType<Test>().configureEach {
    useJUnitPlatform()

    val prefix = "test.system.property."
    systemProperties = project.properties
        .filterKeys { it.length > prefix.length && it.startsWith(prefix) }
        .mapKeys { it.key.substringAfter(prefix) }

    systemProperty("hf.tier", "test")

    // We are deliberately not cleaning up the temporary directory after the
    // tests have been executed in case they contain valuable information that
    // is of use to investigate failed tests. It is the responsibility of the
    // test authors to clean up after them if they require it.
    systemProperty("java.io.tmpdir", temporaryDir.absolutePath)

    testLogging {
        events = setOf(FAILED)
        exceptionFormat = FULL
    }
    testlogger {
        theme = STANDARD_PARALLEL
        showFullStackTraces = true
        showSimpleNames = true
    }
}
