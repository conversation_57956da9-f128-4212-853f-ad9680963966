[versions]
kotlin = "1.9.23"
spring-boot = "3.5.5"
spring-dependency-management = "1.1.7"
detekt = "1.23.6"
jackson = "2.15.4"
jib = "3.4.5"
junit = '5.8.2'
log4j = "2.17.2"
sonarqube = "4.4.1.3373"
springCloud="2025.0.0"

[libraries]
# Gradle plugins dependencies
kotlin-gradle = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlin" }
kotlin-allopen = { module = "org.jetbrains.kotlin:kotlin-allopen", version.ref = "kotlin" }
kotlin-noarg = { module = "org.jetbrains.kotlin:kotlin-noarg", version.ref = "kotlin" }

detekt-gradle = { module = "io.gitlab.arturbosch.detekt:io.gitlab.arturbosch.detekt.gradle.plugin", version.ref = "detekt" }

jib-gradle = { module = "com.google.cloud.tools.jib:com.google.cloud.tools.jib.gradle.plugin", version.ref = "jib" }
test-logger = { module = "com.adarshr:gradle-test-logger-plugin", version = "4.0.0" }

springBoot-gradle = { module = "org.springframework.boot:spring-boot-gradle-plugin", version.ref = "spring-boot" }

# Library dependencies
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jackson" }
mockk = { module = "io.mockk:mockk", version = "1.13.8" }

[plugins]
jacocolog = { id = "org.barfuin.gradle.jacocolog", version = "3.1.0" }
sonarqube = { id = "org.sonarqube", version.ref = "sonarqube" }
