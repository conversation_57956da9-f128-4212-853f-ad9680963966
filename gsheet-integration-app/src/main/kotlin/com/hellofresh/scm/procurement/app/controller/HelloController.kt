package com.hellofresh.scm.procurement.app.controller

import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/api/v1")
class HelloController {

    @GetMapping("/hello")
    fun hello(): ResponseEntity<Map<String, String>> = ResponseEntity.ok(
        mapOf(
            "message" to "Hello World from SCM Procurement Data Integration",
            "service" to "data-integration-app",
            "timestamp" to System.currentTimeMillis().toString()
        )
    )

    @GetMapping("/health")
    fun health(): ResponseEntity<Map<String, String>> = ResponseEntity.ok(
        mapOf(
            "status" to "UP",
            "service" to "data-integration-app"
        )
    )
}
