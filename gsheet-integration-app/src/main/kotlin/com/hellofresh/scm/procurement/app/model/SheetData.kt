package com.hellofresh.scm.procurement.app.model

/**
 * Data class representing a row from the Google Sheet
 */
data class SheetRowData(
    val values: List<String>
) {
    fun getValueAt(index: Int): String? = values.getOrNull(index)
    
    fun size(): Int = values.size
}

/**
 * Data class representing the entire sheet content
 */
data class SheetData(
    val sheetId: String,
    val sheetName: String,
    val headers: List<String>,
    val rows: List<SheetRowData>,
    val totalRows: Int = rows.size
) {
    fun getRowCount(): Int = rows.size
    
    fun getColumnCount(): Int = headers.size
    
    fun isEmpty(): Boolean = rows.isEmpty()
}

/**
 * Configuration for Google Sheets API access
 */
data class GoogleSheetsConfig(
    val applicationName: String = "SCM Procurement Data Integration",
    val credentialsFilePath: String? = null,
    val serviceAccountKey: String? = null
)
