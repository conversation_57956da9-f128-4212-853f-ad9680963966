---
topics:
  procurement-data: public.scm.procurement.data-integration.v1

server:
  http2:
    enabled: true
  max-http-request-header-size: 1MB
  shutdown: graceful

spring:
  application:
    name: data-integration-app
  datasource:
    driverClassName: org.postgresql.Driver
    url: ${DB_URL}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      connection-timeout: 30000 # 30 seconds
      idle-timeout: 300000 # 5 minutes
      maximum-pool-size: 10
      minimum-idle: 2
      connection-test-query: "SELECT 1"
      validation-timeout: 5000 # 5 second
      max-lifetime: 1800000 # 30 minutes
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
    hibernate.ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  lifecycle:
    timeout-per-shutdown-phase: 30s
  servlet:
    multipart:
      max-file-size: 250KB
      max-request-size: 250KB
  cloud:
    function:
      definition: processData
    stream:
      bindings:
        processData-out-0:
          destination: ${topics.procurement-data}
          producer:
            use-native-encoding: true
      kafka:
        binder:
          brokers: ${application.brokerAddress}
          autoCreateTopics: false
          consumer-properties:
            key.deserializer: org.apache.kafka.common.serialization.StringDeserializer
          producer-properties:
            key.serializer: org.apache.kafka.common.serialization.StringSerializer
        bindings:
          processData-out-0:
            producer:
              configuration:
                value.serializer: org.springframework.kafka.support.serializer.JsonSerializer

management:
  endpoint:
    health:
      show-details: "ALWAYS"
      probes:
        enabled: true
  endpoints:
    access:
      default: read_only
    web:
      exposure:
        include: "*"
  server:
    port: 8081
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  info:
    git:
      mode: full
      enabled: true
  tracing:
    propagation:
      type: B3,W3C
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://${OTLP_EXPORTER_HOST:localhost}:9411/api/v2/spans

logging:
  pattern:
    level: "%5p [%MDC{traceId},%MDC{spanId}]"

webclient:
  connection-timeout: 5s
  response-timeout: 5s
  max-idle-time: 20s
  max-life-time: 60s
  pending-acquire-timeout: 60s
  evict-in-background: 120s
