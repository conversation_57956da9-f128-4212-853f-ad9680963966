---
application:
  brokerAddress: ${KAFKA_BROKER_HOST:localhost}:29092

management:
  endpoints:
    access:
      default: read_only
    web:
      exposure:
        include: "*"
  server:
    port: 8081
  endpoint:
    health:
      show-details: "ALWAYS"
      probes:
        enabled: true
  tracing:
    enabled: false

spring:
  profiles:
    active: local
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  h2:
    console:
      enabled: true
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  cloud:
    stream:
      kafka:
        binder:
          autoCreateTopics: true

logging:
  level:
    com.hellofresh.scm.procurement: DEBUG
