# https://logging.apache.org/log4j/2.x/manual/configuration.html#SystemProperties
java.util.logging.manager=org.apache.logging.log4j.jul.LogManager
log4j2.disableJmx=true
log4j2.disableThreadContextStack=true

# We are not using async logging because we are building microservices and
# target pods with limited resources, hence, spawning additional threads will
# most likely result in more overhead than is worth.
#
# https://logging.apache.org/log4j/2.x/manual/async.html
# log4j2.contextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector

# https://logging.apache.org/log4j/2.x/manual/garbagefree.html
log4j2.enableDirectEncoders=true
log4j2.enableThreadlocals=true
log4j2.isWebapp=false
log4j2.garbagefreeThreadContextMap=true
